package gui

import (
	"github.com/hajimehoshi/guigui"
)

type App struct {
	uimgr *guigui.UIManager
}

func (a *App) Update() error {
	if err := a.uimgr.Update(); err != nil {
		return err
	}
	return nil
}

func (a *App) Draw(screen *guigui.Screen) {
	a.uimgr.Draw(screen)
}

func (a *App) Layout(outsideWidth, outsideHeight int) (int, int) {
	return outsideWidth, outsideHeight
}

// Start initializes and starts the GUI
func Start() error {
	a := &App{}
	var err error
	a.uimgr, err = guigui.NewUIManager(a)
	if err != nil {
		return err
	}
	defer a.uimgr.Finalize()

	w, err := a.uimgr.NewWindow("OpenCloud", 800, 600)
	if err != nil {
		return err
	}
	defer w.Finalize()

	for {
		if err := a.uimgr.Update(); err != nil {
			return err
		}
		if w.Closed() {
			return nil
		}
	}
}
