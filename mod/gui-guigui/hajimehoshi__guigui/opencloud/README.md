# Open Cloud + Guigui Integration

This project integrates [`opencloud`](https://github.com/opencloud-eu/opencloud) (a Golang collaboration server) with [`Guigui`](https://github.com/hajimehoshi/guigui) (a Golang GUI framework).

## Goals

- Integrate `opencloud` with `Guigui`.
- Add a Golang-based indexing solution as an alternative to Apache Tika.
- Add support for indexing and searching Markdown files.
- Add support for Decksh presentations.

## Taskfile

This project uses [Task](https://taskfile.dev/) for task automation. The `Taskfile.yml` is in the root of the workspace.

Dont forget that ":" inside echo statements break the taskfile.

## Binaries

This project produces three distinct binaries, each serving a specific purpose:

*   **`opencloud-gui`**: Runs the graphical user interface.
*   **`opencloud`**: Runs the OpenCloud collaboration server.
*   **`opencloud-cli`**: Provides command-line utilities for indexing and searching.

### Building the Binaries

You can build each binary individually using standard Go commands. It's recommended to output them to a `bin` directory (create it if it doesn't exist):
```bash
# Ensure you are in the opencloud project root directory
mkdir -p bin
go build -o bin/opencloud-gui ./cmd/opencloud-gui/main.go
go build -o bin/opencloud-server ./cmd/opencloud-server/main.go
go build -o bin/opencloud-cli ./cmd/opencloud-cli/main.go
```
**Note:** If you are using the `Taskfile.yml`, its build tasks might need to be updated to reflect these new binary paths and commands.

### Running the Binaries

#### `opencloud-gui`
Runs the graphical user interface.
```bash
./bin/opencloud-gui
```

#### `opencloud` (Server)
Runs the collaboration server.
```bash
# Run server on default port 8080 and default index ./index
./bin/opencloud

# Run server on a custom port and with a specific index directory
./bin/opencloud -port 9090 -index ./my_custom_index
```
Flags:
*   `-port <port_number>`: (Default: `8080`) Specifies the port for the server.
*   `-index <path_to_index>`: (Default: `./index`) Specifies the directory for the search index.

#### `opencloud-cli`
Provides command-line interface for indexing and searching.
```bash
# Indexing example (defaults to ./data for data and ./index for index output)
./bin/opencloud-cli -mode index -data ./path/to/your/data -index ./path/to/your/index

```bash
# Searching example (defaults to ./index for index location)
./bin/opencloud-cli -mode search -query "your search term" -index ./path/to/your/index
```
Flags:
*   `-mode <mode_type>`: (Required) Specifies the operation mode.
    *   `index`: To index a data directory.
    *   `search`: To search the index.
*   `-data <path_to_data>`: (Default: `./data`) Path to the data directory to be indexed (used with `-mode index`).
*   `-index <path_to_index>`: (Default: `./index`) Path to the directory where the index is stored or will be created.
*   `-query "<search_query>"`: Search query string (required for `-mode search`).

### Usage

To see available commands and project information, run:
```bash
task
```

To build and run the project:
```bash
task run
```

The project is set up to be developed within a mono repo, with a `go.mod` file in the parent directory.

## Search

`opencloud`'s search functionality is a key part of this integration.

### Current Implementation

- **Indexing:** By default, `opencloud` uses [Apache Tika](https://docs.opencloud.eu/docs/dev/server/Services/search/Search-info/) for indexing various file types.
- **Search Service:** The search service is built using [bleve](https://github.com/opencloud-eu/opencloud/tree/main/services/search).
- **Query Language:** It uses [KQL (Keyword Query Language)](https://github.com/opencloud-eu/opencloud/tree/main/pkg/kql).

### Integration Goals

The main goal is to enhance the search capabilities within the `Guigui` client by:

1.  **Adding a native Golang indexing solution:** This will provide an alternative to the Java-based Apache Tika, simplifying the stack.
2.  **Adding Markdown support:** The new indexer should support `.md` files.
3.  **Adding Decksh support:** Once Markdown support is working, we will add support for `Decksh`.
