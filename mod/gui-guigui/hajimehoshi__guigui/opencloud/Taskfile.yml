version: '3'

vars:
  BIN_DIR: bin
  DATA_DIR: ./data
  INDEX_DIR: ./index

  APP_NAME_SERVER: opencloud
  CMD_DIR_SERVER: ./cmd/opencloud

  APP_NAME_GUI: opencloud-gui
  CMD_DIR_GUI: ./cmd/opencloud-gui

  APP_NAME_CLI: opencloud-cli
  CMD_DIR_CLI: ./cmd/opencloud-cli

tasks:
  default:
    desc: "Show available tasks"
    cmds:
      - echo "🌩️  OpenCloud - Collaboration Server with GUI"
      - echo "============================================="
      - echo ""
      - echo "🏗️  BUILD:"
      - echo "  build       - Build all applications (server, gui, cli)"
      - echo "  clean       - Clean build artifacts"
      - echo ""
      - echo "SEARCH & INDEX:"
      - echo "  index       - Index documents in data directory"
      - echo "  search      - Search indexed documents"
      - echo "                (e.g., task search QUERY=\"your query\")"
      - echo "  demo-index  - Index sample markdown files"
      - echo "  demo-search - Search demo"
      - echo ""
      - echo "RUN:"
      - echo "  gui         - Start GUI application (opencloud-gui)"
      - echo "  server      - Start collaboration server (opencloud)"
      - echo ""
      - echo "🧪 DEVELOPMENT:"
      - echo "  test        - Run tests"
      - echo "  deps        - Update dependencies"
      - echo ""
      - echo "Binaries are output to the '{{.BIN_DIR}}' directory."
      - echo "All available tasks:"
      - task --list-all

  build:
    desc: "Build the OpenCloud application"
    cmds:
      - mkdir -p {{.BIN_DIR}}
      - go build -v -o {{.BIN_DIR}}/{{.APP_NAME_SERVER}} {{.CMD_DIR_SERVER}}/main.go
      - go build -v -o {{.BIN_DIR}}/{{.APP_NAME_GUI}} {{.CMD_DIR_GUI}}/main.go
      - go build -v -o {{.BIN_DIR}}/{{.APP_NAME_CLI}} {{.CMD_DIR_CLI}}/main.go

  clean:
    desc: "Clean build artifacts and indexes"
    cmds:
      - rm -rf {{.BIN_DIR}}
      - rm -rf {{.INDEX_DIR}}
      - echo "Cleaned build artifacts and indexes"

  test:
    desc: "Run tests"
    cmds:
      - go test -v ./...

  deps:
    desc: "Update dependencies"
    cmds:
      - go mod tidy
      - go mod download

  # =============================================================================
  # INDEXING & SEARCH
  # =============================================================================

  index:
    desc: "Index documents in data directory"
    deps: [build]
    cmds:
      - mkdir -p {{.DATA_DIR}} # Ensure data dir exists, though indexing might not need it if already present
      - ./{{.BIN_DIR}}/{{.APP_NAME_CLI}} -mode index -data {{.DATA_DIR}} -index {{.INDEX_DIR}}

  search:
    desc: "Search indexed documents (requires QUERY env var)"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME_CLI}} -mode search -index {{.INDEX_DIR}} -query "{{.QUERY}}"

  demo-index:
    desc: "Create sample data and index it"
    deps: [build]
    cmds:
      - task: create-sample-data
      - task: index

  demo-search:
    desc: "Run search demonstrations"
    deps: [build]
    cmds:
      - echo "🔍 Demo Search 1 - Search for 'markdown'"
      - ./{{.BIN_DIR}}/{{.APP_NAME_CLI}} -mode search -index {{.INDEX_DIR}} -query "markdown"
      - echo ""
      - echo "🔍 Demo Search 2 - Search for Go files"
      - ./{{.BIN_DIR}}/{{.APP_NAME_CLI}} -mode search -index {{.INDEX_DIR}} -query "type:go" # Assuming your CLI search supports this KQL-like syntax
      - echo ""
      - echo "🔍 Demo Search 3 - Search in titles"
      - ./{{.BIN_DIR}}/{{.APP_NAME_CLI}} -mode search -index {{.INDEX_DIR}} -query "title:sample" # Assuming your CLI search supports this

  # =============================================================================
  # GUI & SERVER
  # =============================================================================

  gui:
    desc: "Start GUI mode"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME_GUI}}

  server:
    desc: "Start collaboration server"
    deps: [build]
    cmds:
      - ./{{.BIN_DIR}}/{{.APP_NAME_SERVER}} -port 8080 -index {{.INDEX_DIR}} # Added -index as server might need it

  # =============================================================================
  # UTILITIES
  # =============================================================================

  create-sample-data:
    desc: "Create sample markdown and other files for testing"
    cmds:
      - mkdir -p {{.DATA_DIR}}/docs
      - mkdir -p {{.DATA_DIR}}/code
      - |
        cat > {{.DATA_DIR}}/docs/sample.md << 'EOF'
        ---
        title: Sample Markdown Document
        tags: [sample, markdown, demo]
        language: en
        ---

        # Sample Markdown Document

        This is a **sample markdown document** for testing the OpenCloud indexing system.

        ## Features

        - Markdown processing with metadata
        - Full-text search with Bleve
        - KQL query language support
        - Integration with Guigui framework

        ## Code Example

        ```go
        func main() {
            fmt.Println("Hello, OpenCloud!")
        }
        ```

        ## Tags

        This document is tagged with: sample, markdown, demo
        EOF
      - |
        cat > {{.DATA_DIR}}/docs/collaboration.md << 'EOF'
        ---
        title: Collaboration Features
        tags: [collaboration, server, realtime]
        ---

        # Collaboration Features

        OpenCloud provides real-time collaboration capabilities:

        - **Document sharing** - Share documents with team members
        - **Real-time editing** - Multiple users can edit simultaneously
        - **Version control** - Track changes and history
        - **Search integration** - Find documents quickly

        ## Technical Stack

        - Go backend server
        - Guigui frontend framework
        - Bleve search engine
        - Apache Tika integration
        EOF
      - |
        cat > {{.DATA_DIR}}/code/example.go << 'EOF'
        package main

        import (
            "fmt"
            "log"
        )

        // Example Go code for OpenCloud
        func main() {
            fmt.Println("OpenCloud - Collaboration Server")

            // Initialize search engine
            if err := initializeSearch(); err != nil {
                log.Fatal(err)
            }

            // Start GUI
            startGUI()
        }

        func initializeSearch() error {
            // TODO: Initialize Bleve search
            return nil
        }

        func startGUI() {
            // TODO: Start Guigui interface
        }
        EOF
      - echo "✅ Sample data created in {{.DATA_DIR}}"